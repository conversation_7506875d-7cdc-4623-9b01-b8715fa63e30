name: app
description: FutureLog Webshop Application
publish_to: none

version: 3.15.4+1451

environment:
  sdk: ">=3.4.0 <4.0.0"
  flutter: "3.29.3"

dependencies:
  analyzer: 6.11.0
  animations: 2.0.11
  app_badge_plus: 1.2.1
  app_links: 6.4.0

  bloc_concurrency: 0.3.0
  bottom_sheet: 4.0.0 #TODO: remove after redesign
  cached_network_image: 3.4.1
  camera: 0.11.1
  clock: 1.1.2
  collection: 1.19.1
  convert: 3.1.2
  crypto: 3.0.6
  edge_detection: 1.1.3 #TODO: possibly discontinued
  envy_annotation: 0.0.3
  equatable: 2.0.7
  extended_image: 10.0.0
  file: 7.0.1
  file_picker: 10.1.2
  firebase_core: 3.13.0
  firebase_messaging: 15.2.5
  fl_icons:
    path: ./packages/fl_icons
  fl_ui:
    path: ./packages/fl_ui
  flutter:
    sdk: flutter
  flutter_bloc: 9.1.1
  flutter_bluetooth_serial:
    path: ./packages/flutter_bluetooth_serial
  flutter_image_compress: 2.4.0
  flutter_inappwebview: 6.1.5
  flutter_keyboard_visibility: 6.0.0
  flutter_localizations:
    sdk: flutter
  flutter_reactive_ble: 5.4.0
  flutter_secure_storage: 9.2.4
  flutter_widget_from_html_core: 0.16.0
  formz: 0.8.0
  freezed_annotation: 2.4.4
  get: 4.7.2
  google_mlkit_barcode_scanning: 0.13.0
  http: 1.3.0
  http_parser: 4.1.2
  ieee754: 1.0.3
  image_editor: 1.6.0

  json_annotation: 4.9.0
  material_symbols_icons: 4.2815.0
  math_expressions: 2.7.0
  meta: 1.16.0
  path: 1.9.1
  path_provider: 2.1.5
  pdf: 3.11.3
  pdfrx: 1.1.23
  permission_handler: 12.0.0+1
  photo_view: 0.15.0
  platform_info_repository:
    path: ./packages/platform_info_repository
  printing: 5.14.2
  provider: 6.1.4
  random_string: 2.3.1
  rxdart: 0.27.7
  sentry_flutter: 8.14.1
  share_plus: 10.1.4
  signature: 6.0.0
  speech_to_text: 7.0.0
  sqflite_common_ffi: 2.3.5
  sqlite3_flutter_libs: 0.5.32
  store_launcher:
    path: ./packages/store_launcher
  string_similarity: 2.1.1
  tr_labels_annotation:
    path: ./packages/tr_labels/tr_labels_annotation
  url_launcher: 6.3.1
  version: 3.0.2

dev_dependencies:
  bloc_test: 10.0.0
  build_runner: 2.4.15
  dart_style: 2.3.7
  envy_generator: 0.0.5
  fl_lints:
    path: ./packages/fl_lints
  flutter_launcher_icons: 0.13.1
  flutter_test:
    sdk: flutter
  freezed: 2.5.7
  integration_test:
    sdk: flutter
  json_serializable: 6.9.0
  melos: ^6.3.3
  mockingjay: 0.5.0
  mocktail: 1.0.3
  path_provider_platform_interface: 2.1.2
  plugin_platform_interface: 2.1.8
  share_plus_platform_interface: 5.0.2
  test: 1.25.15
  tr_labels:
    path: ./packages/tr_labels/tr_labels
  url_launcher_platform_interface: 2.3.2

dependency_overrides:
  camera_avfoundation: 0.9.20+1 # TODO: remove when camera will use this version

flutter:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Thin.ttf
          weight: 100
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Black.ttf
          weight: 900

  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/catalog_food_labels/
    - assets/images/integrated_devices/
    - assets/i18n/
